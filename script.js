// Password toggle functionality
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Form switching functionality
function showRegister() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const loginCard = document.querySelector('.login-card');

    // Add slide out animation
    loginForm.style.transform = 'translateX(-100px)';
    loginForm.style.opacity = '0';
    loginCard.style.transform = 'translateY(-10px) rotateY(-10deg)';

    setTimeout(() => {
        loginForm.classList.add('hidden');
        registerForm.classList.remove('hidden');

        // Reset card position
        loginCard.style.transform = 'translateY(-10px) rotateY(10deg)';

        // Slide in register form
        registerForm.style.transform = 'translateX(100px)';
        registerForm.style.opacity = '0';

        setTimeout(() => {
            registerForm.style.transition = 'all 0.5s cubic-bezier(0.23, 1, 0.32, 1)';
            registerForm.style.transform = 'translateX(0)';
            registerForm.style.opacity = '1';
            loginCard.style.transform = 'translateY(0) rotateY(0deg)';
        }, 50);
    }, 300);
}

function showLogin() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const loginCard = document.querySelector('.login-card');

    // Add slide out animation
    registerForm.style.transform = 'translateX(100px)';
    registerForm.style.opacity = '0';
    loginCard.style.transform = 'translateY(-10px) rotateY(10deg)';

    setTimeout(() => {
        registerForm.classList.add('hidden');
        loginForm.classList.remove('hidden');

        // Reset card position
        loginCard.style.transform = 'translateY(-10px) rotateY(-10deg)';

        // Slide in login form
        loginForm.style.transform = 'translateX(-100px)';
        loginForm.style.opacity = '0';

        setTimeout(() => {
            loginForm.style.transition = 'all 0.5s cubic-bezier(0.23, 1, 0.32, 1)';
            loginForm.style.transform = 'translateX(0)';
            loginForm.style.opacity = '1';
            loginCard.style.transform = 'translateY(0) rotateY(0deg)';
        }, 50);
    }, 300);
}

// Form validation and submission
document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    // Basic validation
    if (!email || !password) {
        showNotification('Please fill in all fields', 'error');
        return;
    }
    
    if (!isValidEmail(email)) {
        showNotification('Please enter a valid email address', 'error');
        return;
    }
    
    // Simulate login process
    const loginBtn = document.querySelector('.login-btn');
    const originalText = loginBtn.innerHTML;
    
    loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing in...';
    loginBtn.disabled = true;
    
    setTimeout(() => {
        showNotification('Login successful! Welcome back!', 'success');
        loginBtn.innerHTML = originalText;
        loginBtn.disabled = false;
        
        // Here you would typically redirect or update the UI
        console.log('Login attempt:', { email, password });
    }, 2000);
});

document.getElementById('registerForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const fullName = document.getElementById('fullName').value;
    const email = document.getElementById('regEmail').value;
    const password = document.getElementById('regPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // Basic validation
    if (!fullName || !email || !password || !confirmPassword) {
        showNotification('Please fill in all fields', 'error');
        return;
    }
    
    if (!isValidEmail(email)) {
        showNotification('Please enter a valid email address', 'error');
        return;
    }
    
    if (password.length < 6) {
        showNotification('Password must be at least 6 characters long', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        showNotification('Passwords do not match', 'error');
        return;
    }
    
    // Simulate registration process
    const registerBtn = document.querySelector('.register-btn');
    const originalText = registerBtn.innerHTML;
    
    registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating account...';
    registerBtn.disabled = true;
    
    setTimeout(() => {
        showNotification('Account created successfully! Please check your email to verify.', 'success');
        registerBtn.innerHTML = originalText;
        registerBtn.disabled = false;
        
        // Switch back to login form
        setTimeout(() => {
            showLogin();
        }, 1500);
        
        console.log('Registration attempt:', { fullName, email, password });
    }, 2000);
});

// Social login handlers
document.querySelectorAll('.social-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const platform = this.querySelector('span').textContent;
        showNotification(`Redirecting to ${platform} login...`, 'info');
        
        // Simulate social login redirect
        setTimeout(() => {
            console.log(`${platform} login initiated`);
        }, 1000);
    });
});

// Utility functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas ${getNotificationIcon(type)}"></i>
        <span>${message}</span>
        <button class="close-notification" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        default: return 'fa-info-circle';
    }
}

// Input animation enhancements
document.querySelectorAll('input').forEach(input => {
    input.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');

        // Add glow effect
        this.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.3)';
    });

    input.addEventListener('blur', function() {
        this.parentElement.classList.remove('focused');
        this.style.boxShadow = '';
    });

    // Add hover effects
    input.addEventListener('mouseenter', function() {
        if (this !== document.activeElement) {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
        }
    });

    input.addEventListener('mouseleave', function() {
        if (this !== document.activeElement) {
            this.style.transform = '';
            this.style.boxShadow = '';
        }
    });

    // Add ripple effect on focus
    input.addEventListener('focus', function() {
        const ripple = document.createElement('div');
        ripple.className = 'input-ripple';
        this.parentElement.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    });
});

// Add floating particles effect
function createFloatingParticles() {
    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'floating-particles';
    document.body.appendChild(particlesContainer);
    
    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 10 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        particlesContainer.appendChild(particle);
    }
}

// Initialize particles on page load
document.addEventListener('DOMContentLoaded', function() {
    createFloatingParticles();

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Add entrance animation to the card
    const loginCard = document.querySelector('.login-card');
    loginCard.style.opacity = '0';
    loginCard.style.transform = 'translateY(50px) scale(0.9)';

    setTimeout(() => {
        loginCard.style.transition = 'all 0.8s cubic-bezier(0.23, 1, 0.32, 1)';
        loginCard.style.opacity = '1';
        loginCard.style.transform = 'translateY(0) scale(1)';
    }, 100);

    // Enhanced button hover effects
    document.querySelectorAll('.login-btn, .social-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });

        btn.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(-1px) scale(0.98)';
        });

        btn.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });
    });
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Enter key to submit active form
    if (e.key === 'Enter' && e.target.tagName !== 'BUTTON') {
        const activeForm = document.querySelector('form:not(.hidden)');
        if (activeForm) {
            const submitBtn = activeForm.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.click();
            }
        }
    }
    
    // Escape key to clear notifications
    if (e.key === 'Escape') {
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach(notification => notification.remove());
    }
});

// Add mouse parallax effect
document.addEventListener('mousemove', function(e) {
    const shapes = document.querySelectorAll('.shape');
    const mouseX = e.clientX / window.innerWidth;
    const mouseY = e.clientY / window.innerHeight;
    
    shapes.forEach((shape, index) => {
        const speed = (index + 1) * 0.5;
        const x = (mouseX - 0.5) * speed * 20;
        const y = (mouseY - 0.5) * speed * 20;
        
        shape.style.transform = `translate(${x}px, ${y}px)`;
    });
});
