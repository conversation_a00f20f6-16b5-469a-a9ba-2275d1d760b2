// Password toggle functionality
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Enhanced Form switching functionality
function showRegister() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const loginCard = document.querySelector('.login-card');

    console.log('Switching to register form'); // Debug log

    // Dramatic slide out animation
    loginForm.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
    loginForm.style.transform = 'translateX(-150px) rotateY(-15deg) scale(0.8)';
    loginForm.style.opacity = '0';

    // Card flip animation
    loginCard.style.transition = 'all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
    loginCard.style.transform = 'translateY(-20px) rotateY(-20deg) scale(0.95)';

    setTimeout(() => {
        loginForm.classList.add('hidden');
        registerForm.classList.remove('hidden');

        // Prepare register form for slide in
        registerForm.style.transition = 'none';
        registerForm.style.transform = 'translateX(150px) rotateY(15deg) scale(0.8)';
        registerForm.style.opacity = '0';

        // Animate register form in
        setTimeout(() => {
            registerForm.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
            registerForm.style.transform = 'translateX(0) rotateY(0deg) scale(1)';
            registerForm.style.opacity = '1';

            // Reset card
            loginCard.style.transform = 'translateY(0) rotateY(0deg) scale(1)';
        }, 100);
    }, 400);
}

function showLogin() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const loginCard = document.querySelector('.login-card');

    console.log('Switching to login form'); // Debug log

    // Dramatic slide out animation
    registerForm.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
    registerForm.style.transform = 'translateX(150px) rotateY(15deg) scale(0.8)';
    registerForm.style.opacity = '0';

    // Card flip animation
    loginCard.style.transition = 'all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
    loginCard.style.transform = 'translateY(-20px) rotateY(20deg) scale(0.95)';

    setTimeout(() => {
        registerForm.classList.add('hidden');
        loginForm.classList.remove('hidden');

        // Prepare login form for slide in
        loginForm.style.transition = 'none';
        loginForm.style.transform = 'translateX(-150px) rotateY(-15deg) scale(0.8)';
        loginForm.style.opacity = '0';

        // Animate login form in
        setTimeout(() => {
            loginForm.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
            loginForm.style.transform = 'translateX(0) rotateY(0deg) scale(1)';
            loginForm.style.opacity = '1';

            // Reset card
            loginCard.style.transform = 'translateY(0) rotateY(0deg) scale(1)';
        }, 100);
    }, 400);
}

// Form validation and submission
document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    // Basic validation
    if (!email || !password) {
        showNotification('Please fill in all fields', 'error');
        return;
    }
    
    if (!isValidEmail(email)) {
        showNotification('Please enter a valid email address', 'error');
        return;
    }
    
    // Simulate login process
    const loginBtn = document.querySelector('.login-btn');
    const originalText = loginBtn.innerHTML;
    
    loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing in...';
    loginBtn.disabled = true;
    
    setTimeout(() => {
        showNotification('Login successful! Welcome back!', 'success');
        loginBtn.innerHTML = originalText;
        loginBtn.disabled = false;
        
        // Here you would typically redirect or update the UI
        console.log('Login attempt:', { email, password });
    }, 2000);
});

document.getElementById('registerForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const fullName = document.getElementById('fullName').value;
    const email = document.getElementById('regEmail').value;
    const password = document.getElementById('regPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // Basic validation
    if (!fullName || !email || !password || !confirmPassword) {
        showNotification('Please fill in all fields', 'error');
        return;
    }
    
    if (!isValidEmail(email)) {
        showNotification('Please enter a valid email address', 'error');
        return;
    }
    
    if (password.length < 6) {
        showNotification('Password must be at least 6 characters long', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        showNotification('Passwords do not match', 'error');
        return;
    }
    
    // Simulate registration process
    const registerBtn = document.querySelector('.register-btn');
    const originalText = registerBtn.innerHTML;
    
    registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating account...';
    registerBtn.disabled = true;
    
    setTimeout(() => {
        showNotification('Account created successfully! Please check your email to verify.', 'success');
        registerBtn.innerHTML = originalText;
        registerBtn.disabled = false;
        
        // Switch back to login form
        setTimeout(() => {
            showLogin();
        }, 1500);
        
        console.log('Registration attempt:', { fullName, email, password });
    }, 2000);
});

// Social login handlers
document.querySelectorAll('.social-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const platform = this.querySelector('span').textContent;
        showNotification(`Redirecting to ${platform} login...`, 'info');
        
        // Simulate social login redirect
        setTimeout(() => {
            console.log(`${platform} login initiated`);
        }, 1000);
    });
});

// Utility functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas ${getNotificationIcon(type)}"></i>
        <span>${message}</span>
        <button class="close-notification" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        default: return 'fa-info-circle';
    }
}

// Input animation enhancements
document.querySelectorAll('input').forEach(input => {
    input.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');

        // Add glow effect
        this.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.3)';
    });

    input.addEventListener('blur', function() {
        this.parentElement.classList.remove('focused');
        this.style.boxShadow = '';
    });

    // Add hover effects
    input.addEventListener('mouseenter', function() {
        if (this !== document.activeElement) {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
        }
    });

    input.addEventListener('mouseleave', function() {
        if (this !== document.activeElement) {
            this.style.transform = '';
            this.style.boxShadow = '';
        }
    });

    // Add ripple effect on focus
    input.addEventListener('focus', function() {
        const ripple = document.createElement('div');
        ripple.className = 'input-ripple';
        this.parentElement.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    });
});

// Add floating particles effect
function createFloatingParticles() {
    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'floating-particles';
    document.body.appendChild(particlesContainer);
    
    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 10 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        particlesContainer.appendChild(particle);
    }
}

// Initialize all effects on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing effects...'); // Debug log

    createFloatingParticles();

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Add entrance animation to the card
    const loginCard = document.querySelector('.login-card');
    loginCard.style.opacity = '0';
    loginCard.style.transform = 'translateY(50px) scale(0.9)';

    setTimeout(() => {
        loginCard.style.transition = 'all 0.8s cubic-bezier(0.23, 1, 0.32, 1)';
        loginCard.style.opacity = '1';
        loginCard.style.transform = 'translateY(0) scale(1)';
    }, 100);

    // EXPLICIT CARD HOVER EFFECTS
    loginCard.addEventListener('mouseenter', function() {
        console.log('Card hover enter'); // Debug log
        this.style.transform = 'translateY(-20px) rotateX(10deg) rotateY(5deg) scale(1.02)';
        this.style.boxShadow = `
            0 50px 100px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.5),
            0 0 80px rgba(102, 126, 234, 0.4),
            inset 0 0 50px rgba(255, 255, 255, 0.1)
        `;
    });

    loginCard.addEventListener('mouseleave', function() {
        console.log('Card hover leave'); // Debug log
        this.style.transform = 'translateY(0) rotateX(0deg) rotateY(0deg) scale(1)';
        this.style.boxShadow = `
            0 20px 40px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2)
        `;
    });

    // EXPLICIT BUTTON HOVER EFFECTS
    document.querySelectorAll('.login-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            console.log('Button hover enter'); // Debug log
            this.style.transform = 'translateY(-5px) scale(1.05)';
            this.style.boxShadow = '0 20px 40px rgba(102, 126, 234, 0.4)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '';
        });
    });

    // EXPLICIT SOCIAL BUTTON HOVER EFFECTS
    document.querySelectorAll('.social-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            console.log('Social button hover enter'); // Debug log
            this.style.transform = 'translateY(-5px) scale(1.08)';
            this.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.2)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '';
        });
    });

    // EXPLICIT INPUT HOVER EFFECTS
    document.querySelectorAll('input').forEach(input => {
        input.addEventListener('mouseenter', function() {
            if (this !== document.activeElement) {
                console.log('Input hover enter'); // Debug log
                this.style.transform = 'translateY(-2px) scale(1.01)';
                this.style.boxShadow = '0 8px 20px rgba(102, 126, 234, 0.15)';
            }
        });

        input.addEventListener('mouseleave', function() {
            if (this !== document.activeElement) {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '';
            }
        });

        input.addEventListener('focus', function() {
            console.log('Input focus'); // Debug log
            this.style.transform = 'translateY(-3px) scale(1.02)';
            this.style.boxShadow = '0 15px 35px rgba(102, 126, 234, 0.25)';
        });

        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '';
        });
    });
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Enter key to submit active form
    if (e.key === 'Enter' && e.target.tagName !== 'BUTTON') {
        const activeForm = document.querySelector('form:not(.hidden)');
        if (activeForm) {
            const submitBtn = activeForm.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.click();
            }
        }
    }
    
    // Escape key to clear notifications
    if (e.key === 'Escape') {
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach(notification => notification.remove());
    }
});

// Add mouse parallax effect
document.addEventListener('mousemove', function(e) {
    const shapes = document.querySelectorAll('.shape');
    const mouseX = e.clientX / window.innerWidth;
    const mouseY = e.clientY / window.innerHeight;
    
    shapes.forEach((shape, index) => {
        const speed = (index + 1) * 0.5;
        const x = (mouseX - 0.5) * speed * 20;
        const y = (mouseY - 0.5) * speed * 20;
        
        shape.style.transform = `translate(${x}px, ${y}px)`;
    });
});
